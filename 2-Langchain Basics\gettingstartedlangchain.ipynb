import os
from dotenv import load_dotenv
load_dotenv()

## OpenAI API Key
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

## Langsmith Tracking and Tracing
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGCHAIN_API_KEY")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGCHAIN_PROJECT")
os.environ["LANGCHAIN_TRACING_V2"] = "true"

## Groq API Key
os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY")

from langchain_openai import ChatOpenAI

llm = ChatOpenAI(model="o1-mini")

result = llm.invoke("What is agentic AI?")
print(result.content)

from langchain_groq import ChatGroq

llm = ChatGroq(model="qwen-qwq-32b")

result = llm.invoke("What is agentic AI?")
print(result.content)

### Prompt Engineering

from langchain_core.prompts import ChatPromptTemplate
prompt = ChatPromptTemplate.from_messages(
    [
        ("system", "You are an expert AI Engineer. Provide me an answer based on teh question."),
        ("user", "{input}")
    ]
)


from langchain_groq import ChatGroq
model = ChatGroq(model="gemma2-9b-it")
chain = prompt | model
chain

response = chain.invoke({"input": "Can you tell me about langsmith"})
print(response.content)

### Output Parsers

from langchain_core.output_parsers import StrOutputParser

output_parser = StrOutputParser()
chain = prompt | model | output_parser
response = chain.invoke({"input": "Can you tell me about langsmith"})
print(response)

### Output Parsers - JSON

from langchain_core.output_parsers import JsonOutputParser
from pprint import pprint

json_parser = JsonOutputParser()
prompt = ChatPromptTemplate.from_messages(
    [
        ("system", f"You are an expert AI Engineer. Provide the answer strictly in this JSON format:\n{json_parser.get_format_instructions()}"),
        ("user", "{input}")
    ]
)
prompt


chain = prompt | model
response = chain.invoke({"input": "What are the top 3 programming languages?"})
print(response.content)

chain = prompt | model | json_parser
response = chain.invoke({"input": "What are the top 3 programming languages?"})
print(response)

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import XMLOutputParser
xml_parser = XMLOutputParser()
xml_prompt = ChatPromptTemplate.from_messages(
    [
        ("system", f"You are an expert AI Engineer. Provide the answer strictly in this XML format:\n{xml_parser.get_format_instructions()}"),
        ("user", "{input}")
    ]
)
xml_prompt

chain = xml_prompt | model
xml_response = chain.invoke({"input": "What are the top 3 programming languages?"})
print(xml_response.content)

chain = xml_prompt | model | xml_parser
xml_response = chain.invoke({"input": "What are the top 3 programming languages?"})
print(xml_response)


from langchain_core.output_parsers import XMLOutputParser
from langchain_core.prompts import PromptTemplate


actor_query = "Generate the shortened filmography for Tom Hanks."

output = model.invoke(
    f"""{actor_query}
Please enclose the movies in <movie></movie> tags"""
)

print(output.content)

from langchain.output_parsers import YamlOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_groq import ChatGroq
from pydantic import BaseModel, Field

# Define your desired data structure using Pydantic
class Joke(BaseModel):
    setup: str = Field(description="question to setup a joke")
    punchline: str = Field(description="answer to resolve the joke")
    
model = ChatGroq(model="gemma2-9b-it", temperature=0.9)

# And a query intented to prompt a language model to populate the data structure.
joke_query = "Tell me a joke about cats."

# Set up a parser + inject instructions into the prompt template.
parser = YamlOutputParser(pydantic_object=Joke)
#print(parser.get_format_instructions())    

prompt = PromptTemplate(
    template = "Answer the user query.\n{format_instructions}\n{query}",
    input_variables=["query"],
    partial_variables={"format_instructions": parser.get_format_instructions()},
)

chain = prompt | model | parser
response = chain.invoke({"query": joke_query})
print(response)


