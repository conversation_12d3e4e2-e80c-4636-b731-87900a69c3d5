from dotenv import load_dotenv
load_dotenv()

from langchain_groq import ChatGroq
from IPython.display import Markdown, display
from langchain_experimental.utilities import PythonRE<PERSON>
from typing import Annotated
from langchain.agents import tool


llm = ChatGroq(model="deepseek-r1-distill-llama-70b")

response = llm.invoke("What is the capital of France?").content

display(Markdown(response))

import os
from langchain_community.tools.tavily_search import TavilySearchResults
TAVILY_API_KEY=os.getenv("TAVILY_API_KEY")
search_tool=TavilySearchResults(tavily_api_key=TAVILY_API_KEY)

search_tool.invoke("who is a current pm of Canada?")

code = """
x =10
y = x + 10
print(y)
"""

repl = PythonREPL()

repl.run(code)

@tool
def python_repl_tool(
    code: Annotated[str, "The python code to execute to generate your chart."],
):
    """Use this to execute python code. If you want to see the output of a value,
    you should print it out with `print(...)`. This is visible to the user."""
    
    try:
        result = repl.run(code)
    except BaseException as e:
        return f"Failed to execute. Error: {repr(e)}"
    
    result_str = f"Successfully executed:\n\`\`\`python\n{code}\n\`\`\`\nStdout: {result}"
    return (
        result_str + "\n\nIf you have completed all tasks, respond with FINAL ANSWER."
    )
