### text loader
from langchain.document_loaders import TextLoader

# Load text data from a file using TextLoader
loader = TextLoader("./data/speech.txt")
loader


text_document = loader.load()
text_document

# Read the content of the text document
text_content = text_document[0].page_content
print(text_content[:200])

### Read the pdf file
from langchain.document_loaders import PyPDFLoader

# Load PDF data from a file using PyPDFLoader
loader = PyPDFLoader("./data/syllabus.pdf")
loader

pdf_document = loader.load()
pdf_document

# Read the content of the PDF document
pdf_content = pdf_document[0].page_content
print(pdf_content)

## Web based loader
from langchain.document_loaders import WebBaseLoader
import bs4

loader=WebBaseLoader(web_paths=("https://python.langchain.com/docs/integrations/document_loaders/",),)
docs=loader.load()
docs

