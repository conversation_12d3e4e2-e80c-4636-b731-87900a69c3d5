from dotenv import load_dotenv

load_dotenv()

from langchain_openai import ChatOpenAI
llm = ChatOpenAI()

llm.invoke("Hello, how are you?").content

#class AgentState(TypedDict):
#    messages: Annotated[Sequence[BaseMessage], add_messages]

# Sending command from one agen to the other
from langgraph.types import Command

from langgraph.prebuilt import create_react_agent

def add_number(state):
    result = state["num1"] + state["num2"]
    print(f"Addition is {result}")
    return Command(goto="multiply", update={"sum": result})

state={"num1": 10, "num2": 20}

add_number(state)

from langchain_core.tools import tool
from IPython.display import Markdown, display

@tool
def transfer_to_multiplication_expert(sum: int, num3: int) -> int:
    """Transfer the task to a multiplication expert."""
    return sum * num3

@tool
def transfer_to_addition_expert(num1: int, num2: int) -> int:
    """Transfer the task to an addition expert."""
    return num1 + num2

llm_with_tools = llm.bind_tools([transfer_to_multiplication_expert, transfer_to_addition_expert])

response = llm.invoke("Hello, how are you?")
response.content

response.tool_calls

response_with_tools = llm_with_tools.invoke("Can you add 10 and 20 and then multiply the result by 30?")

response_with_tools.content

response_with_tools.tool_calls

system_prompt = (
    "You are an addition expert, you can ask the multiplication expert for help with multiplication.""Always do your portion of calculation before the handoff.")

messages = [{"role": "system", "content": system_prompt}] + ["can you tell me the addition of 2 and 2?"]

messages

from typing_extensions import Literal
from langgraph.graph import StateGraph, MessagesState, START, END

def addition_expert(state:MessagesState)-> Command[Literal["multiplication_expert", "__end__"]]:
    system_prompt = (
        "You are an addition expert, you can ask the multiplication expert for help with multiplication."
        "Always do your portion of calculation before the handoff."
    )
    
    messages = [{"role": "system", "content": system_prompt}] + state["messages"]
    
    
    ai_msg = llm.bind_tools([transfer_to_multiplication_expert]).invoke(messages)
    
    
    if len(ai_msg.tool_calls) > 0:
        tool_call_id = ai_msg.tool_calls[-1]["id"]
        tool_msg = {
            "role": "tool",
            "content": "Successfully transferred",
            "tool_call_id": tool_call_id,
        }
        
        return Command(
            goto="multiplication_expert", update={"messages": [ai_msg, tool_msg]}
        )
    return {"messages": [ai_msg]}


def multiplication_expert(state:MessagesState)-> Command[Literal["addition_expert", "__end__"]]:
    system_prompt = (
        "You are an multiplication expert, you can ask the addition expert for help with addition."
        "Always do your portion of calculation before the handoff."
    )
    
    messages = [{"role": "system", "content": system_prompt}] + state["messages"]
    
    
    ai_msg = llm.bind_tools([transfer_to_addition_expert]).invoke(messages)
    
    
    if len(ai_msg.tool_calls) > 0:
        tool_call_id = ai_msg.tool_calls[-1]["id"]
        tool_msg = {
            "role": "tool",
            "content": "Successfully transferred",
            "tool_call_id": tool_call_id,
        }
        
        return Command(
            goto="addition_expert", update={"messages": [ai_msg, tool_msg]}
        )
    return {"messages": [ai_msg]}

graph = StateGraph(MessagesState)

graph.add_node("addition_expert", addition_expert)

graph.add_node("multiplication_expert", multiplication_expert)

graph.add_edge(START, "addition_expert")

app = graph.compile()

app.invoke({"messages":[("user","what's (3 + 5) * 12. Provide me the output")]})

# display(Markdown())

from langchain_community.tools import DuckDuckGoSearchRun

# search_tool=DuckDuckGoSearchRun()

import os
from langchain_community.tools.tavily_search import TavilySearchResults
TAVILY_API_KEY=os.getenv("TAVILY_API_KEY")
search_tool=TavilySearchResults(tavily_api_key=TAVILY_API_KEY)

search_tool.invoke("who is a current pm of uk?")

from langchain_experimental.utilities import PythonREPL

repl=PythonREPL()

code = """
x = 5
y = x * 2
print(y)
"""

repl.run(code)

from typing import Annotated

@tool
def python_repl_tool(
    code: Annotated[str, "The python code to execute to generate your chart."],
):
    """Use this to execute python code. If you want to see the output of a value,
    you should print it out with `print(...)`. This is visible to the user."""
    
    try:
        result = repl.run(code)
    except BaseException as e:
        return f"Failed to execute. Error: {repr(e)}"
    
    result_str = f"Successfully executed:\n\`\`\`python\n{code}\n\`\`\`\nStdout: {result}"
    return (
        result_str + "\n\nIf you have completed all tasks, respond with FINAL ANSWER."
    )

python_repl_tool

print(python_repl_tool.invoke(code))

def make_system_prompt(instruction:str)->str:
    return  (
        "You are a helpful AI assistant, collaborating with other assistants."
        " Use the provided tools to progress towards answering the question."
        " If you are unable to fully answer, that's OK, another assistant with different tools "
        " will help where you left off. Execute what you can to make progress."
        " If you or any of the other assistants have the final answer or deliverable,"
        " prefix your response with FINAL ANSWER so the team knows to stop."
        f"\n{instruction}"
    )

make_system_prompt("You can only do research. You are working with a chart generator colleague.")

from langchain_core.messages import BaseMessage, HumanMessage

def get_next_node(last_message:BaseMessage, goto:str):
    if "FINAL ANSWER" in last_message.content:
        # Any agent decided the work is done
        return END
    return goto

#agent1
def research_node(state:MessagesState)->Command[Literal["chart_generator", END]]:
    research_agent=create_react_agent(
        llm,
        tools=[search_tool],
        prompt=make_system_prompt(
        "You can only do research. You are working with a chart generator colleague."
    ), 
        )
    
    result=research_agent.invoke(state)
    goto=get_next_node(result["messages"][-1],"chart_generator")
    result["messages"][-1] = HumanMessage(content=result["messages"][-1].content, name="researcher")
    return Command(update={"messages": result["messages"]},goto=goto)

#agent2
def chart_node(state:MessagesState)-> Command[Literal["researcher", END]]:
    chart_agent=create_react_agent(
        llm,
        tools=[python_repl_tool],
        prompt=make_system_prompt(
        "You can only generate charts. You are working with a researcher colleague."
    ),
        )
    result=chart_agent.invoke(state)
    goto=get_next_node(result["messages"][-1],"researcher")
    result["messages"][-1] = HumanMessage(content=result["messages"][-1].content, name="chart_generator")
    return Command(update={"messages": result["messages"]},goto=goto)

workflow = StateGraph(MessagesState)
workflow.add_node("researcher", research_node)
workflow.add_node("chart_generator", chart_node)

workflow.add_edge(START, "researcher")
app = workflow.compile()

workflow.compile()

app.invoke({"messages": [("user","get the UK's GDP over the past 3 years, then make a line chart of it.Once you make the chart, finish.")],})

code="""import matplotlib.pyplot as plt\\n\\n# Data for the UK GDP over the past 3 years\\nyears = [2019, 2020, 2021]\\ngdp_values = [2851.41, 2697.81, 3141.51]  # in billion $ \\n\\ndef create_line_chart(years, gdp_values):\\n    plt.figure(figsize=(10, 6))\\n    plt.plot(years, gdp_values, marker=\'o\', color=\'b\', linestyle=\'-\', linewidth=2)\\n    plt.title(\'UK GDP Over the Past 3 Years\')\\n    plt.xlabel(\'Year\')\\n    plt.ylabel(\'GDP (in billion $)\')\\n    plt.grid(True)\\n    plt.tight_layout()\\n    plt.show()"""

print("""import matplotlib.pyplot as plt\\n\\n# Data for the UK GDP over the past 3 years\\nyears = [2019, 2020, 2021]\\ngdp_values = [2851.41, 2697.81, 3141.51]  # in billion $ \\n\\ndef create_line_chart(years, gdp_values):\\n    plt.figure(figsize=(10, 6))\\n    plt.plot(years, gdp_values, marker=\'o\', color=\'b\', linestyle=\'-\', linewidth=2)\\n    plt.title(\'UK GDP Over the Past 3 Years\')\\n    plt.xlabel(\'Year\')\\n    plt.ylabel(\'GDP (in billion $)\')\\n    plt.grid(True)\\n    plt.tight_layout()\\n    plt.show()""")