from dotenv import load_dotenv
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_groq import ChatGroq

# Load environment variables from .env file
load_dotenv()

# Create a ChatGroq instance
model = ChatGroq(model="llama-3.1-8b-instant", temperature=0.0)

# SystemMessage: Message for priming AI behavior, usually passed in as the first of a sequenc of input messages.
# HumanMessagse: Message from a human to the AI model.

messages = [
    SystemMessage(content="Solve the following math problems"),
    HumanMessage(content="What is 81 divided by 9?"),
]

# Invoke the model with messages
result = model.invoke(messages)
print(f"Answer from AI: {result.content}")

# AIMessage: Message from an AI.
messages = [
    SystemMessage(content="Solve the following math problems"),
    HumanMessage(content="What is 81 divided by 9?"),
    AIMessage(content="To find the answer, we need to divide 81 by 9. 81 ÷ 9 = 9"),
    HumanMessage(content="What is 10 times 5?"),
]

# Invoke the model with messages
result = model.invoke(messages)
print(f"Answer from AI: {result.content}")